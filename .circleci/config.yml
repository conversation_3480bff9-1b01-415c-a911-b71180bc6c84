---
version: 2.1

orbs:
  node: circleci/node@5.0

jobs:
  build:
    working_directory: ~/feedmob_client_reports
    docker:
      - image: cimg/ruby:3.1.4-browsers
        environment:
          RAILS_ENV: test
          DATABASE_URL: 'postgres://app:superinsecure@localhost:5432/app'
          REALTIME_DATABASE_URL: postgres://app:superinsecure@127.0.0.1:5432/app
          REDSHIFT_FEEDMOB_DATABASE_URL: 'postgres://app:superinsecure@127.0.0.1:5432/redshift_main_test'
          FEEDMOB_CLICKS_REDSHIFT_URL: 'postgres://app:superinsecure@127.0.0.1:5432/redshift_click_test'
          AGGREGATED_STATS_READONLY_DATABASE_URL: 'postgres://app:superinsecure@127.0.0.1:5432/conversion_test'
          CONVERSION_DATABASE_URL: 'postgres://app:superinsecure@127.0.0.1:5432/conversion_records_test'
          AGENCY_CONVERSION_DATABASE_URL: 'postgres://app:superinsecure@127.0.0.1:5432/agency_conversion_records_test'

          CI_LOG_ENDPOINT: 'https://ci-api.feedmob.com'
          AUTH_TOKEN_DOMAIN: localhost
          HMAC_SECRET: feedmob
          SSO_SUBDOMAIN: "sso-test"

          REPORT_BACKUP_BUCKET_STAGE: 'feedmob-client-reports-backup-stage'
          REPORT_BUCKET_NAME: 'feedmob-client-reports-testing'

          SENDGRID_API_KEY: 'test'
          ELASTIC_APM_ACTIVE: false
          ELASTIC_APM_ENABLED: false
          GRPC_SERVER_URL: "stage-internal-grpc.feedmob.info:9002"

      - image: cimg/postgres:15.5-postgis
        environment:
          POSTGRES_USER: app
          POSTGRES_DB: app
          POSTGRES_PASSWORD: superinsecure

      - image: redis

    steps:
      - checkout

      - node/install:
          install-yarn: true
          node-version: '8.16'

      - restore_cache:
          keys:
            - feedmob-client_reports-gem-cache-v1-{{ arch }}-{{ .Branch }}-{{ checksum "Gemfile.lock" }}
            - feedmob-client_reports-gem-cache-v1-{{ arch }}-{{ .Branch }}
            - feedmob-client_reports-gem-cache-v1

      - run: bundle config gems.contribsys.com $SIDEKIQ_PRO_NAME:$SIDEKIQ_PRO_PASS


      # Bundle install dependencies
      - run: bundle install --path vendor/bundle

      #开始
      - run: bundle exec rake workflow:generate_circleci_start workflow_key=$CIRCLE_WORKFLOW_ID branch=<< pipeline.git.branch >>

      - save_cache:
          key: feedmob-client_reports-gem-cache-v1-{{ arch }}-{{ .Branch }}-{{ checksum "Gemfile.lock" }}
          paths:
            - vendor/bundle

      - run: bundle exec yarn install

      - save_cache:
          key: feedmob_client_reports-yarn-cache-v1-{{ arch }}-{{ .Branch }}-{{ checksum "yarn.lock" }}
          paths:
            - node_modules

      # Setup the database
      - run: bundle exec rake db:setup

      - run: RAILS_ENV=test bundle exec rails test:load_data_views

      # run Brakeman
      - run: bundle exec brakeman -o coverage/brakeman/output.html -o coverage/brakeman/output.json 2>&1 || true

      - store_artifacts:
          path: coverage/brakeman

      # Run RSpec
      - run: bundle exec rspec spec/

      - store_artifacts:
          path: coverage/rspec

      # Run Cucumber
      - run: bundle exec cucumber features/

      - store_artifacts:
          path: coverage/cucumber

      #结束
      - run: bundle exec rake workflow:generate_circleci_end workflow_key=$CIRCLE_WORKFLOW_ID branch=<< pipeline.git.branch >>
