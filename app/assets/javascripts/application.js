// This is a manifest file that'll be compiled into application.js, which will include all the files
// listed below.
//
// Any JavaScript/Coffee file within this directory, lib/assets/javascripts, or any plugin's
// vendor/assets/javascripts directory can be referenced here using a relative path.
//
// It's not advisable to add code directly here, but if you do, it'll appear at the bottom of the
// compiled file. JavaScript code in this file should be added after the last require_* statement.
//
// Read Sprockets README (https://github.com/rails/sprockets#sprockets-directives) for details
// about supported directives.
//
//= require jquery
//= require jquery-fileupload/basic
//= require bootstrap-sprockets
//= require rails-ujs
//= require turbolinks
//= require moment/moment
//= require metismenu/dist/metisMenu
//= require daterangepicker
//= require select2/dist/js/select2.full
//= require tooltipster
//= require quill.min
//= require quill.global
//= require feedmob/sidebar/sidebar
//= require toastr/build/toastr.min
//= require ahoy

function domReady() {
  ahoy.trackView();

  $('.select2').select2();

  $('.tag-select2').select2({
    tags: true
  });

  var toolbarOptions = [
    [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
    ['bold', 'italic', 'underline'],
    ['link'],
    [{ 'list': 'ordered'}, { 'list': 'bullet' }, { 'list': 'check' }],
    ['clean']
  ];

  $.fn.initEditor = function(id, hidden_field_id, mentionable) {
    var defaults = { theme: 'snow' }
    defaults['modules'] = {}
    defaults['modules']['toolbar'] = toolbarOptions

    var quill = new Quill(id, defaults)
    $(this).submit(function() {
      $(hidden_field_id).val(quill.root.innerHTML);
    })
    return quill
  }

  $(".daterange").each(function() {
    const _this = $(this);
    const minimumDate = _this.data('min-date');
    const cancelLabel = _this.data('cancel-label');
    const autoApply = _this.data('auto-apply');
    const autoUpdateInput = _this.data('auto-update-input');

    const localOptions = {format: 'MM/DD/YYYY'};
    if(cancelLabel){
      localOptions.cancelLabel = cancelLabel;
    }
    $(this).daterangepicker({
      locale: localOptions,
      autoApply: autoApply === false ? false : true,
      autoUpdateInput: autoUpdateInput === false ? false : true,
      alwaysShowCalendars: true,
      showCustomRangeLabel: false,
      minDate: minimumDate ? moment.utc(minimumDate) : moment.utc().subtract(90, 'days').startOf('month'),
      maxDate: moment.utc().endOf('day'),
      ranges: {
        'Today': [
          moment.utc(), moment.utc()
        ],
        'Yesterday': [
          moment.utc().subtract(1, 'days'),
          moment.utc().subtract(1, 'days')
        ],
        'Last 7 Days': [
          moment.utc().subtract(7, 'days'),
          moment.utc().subtract(1, 'days')
        ],
        'Last 30 Days': [
          moment.utc().subtract(30, 'days'),
          moment.utc().subtract(1, 'days')
        ],
        'This Month': [
          moment.utc().startOf('month'), moment.utc().endOf('month')
        ],
        'Last Month': [
          moment.utc().subtract(1, 'month').startOf('month'),
          moment.utc().subtract(1, 'month').endOf('month')
        ]
      }
    }).attr('autocomplete','off');
  });

  $('.daterange').on('apply.daterangepicker', function(evt, picker) {
    $(this).val(picker.startDate.format('MM/DD/YYYY') + ' - ' + picker.endDate.format('MM/DD/YYYY'));
  });

  $('.daterange').on('cancel.daterangepicker', function(evt, picker) {
    //do something, like clearing an input
    $(this).val('');
  });

  $('.single_datepicker').daterangepicker({
    singleDatePicker: true,
    minDate: moment.utc().subtract(90, 'days').startOf('month'),
    maxDate: moment.utc(),
  }).attr('autocomplete','off');

  $('.allow-empty-single-datepicker').daterangepicker({
    singleDatePicker: true,
    autoUpdateInput: false,
    minDate: moment.utc().subtract(90, 'days').startOf('month'),
    maxDate: moment.utc(),
    locale: {
      format: 'YYYY-MM-DD'
    }
  });
  $('.allow-empty-single-datepicker').on('apply.daterangepicker', function(ev, picker) {
      $(this).val(picker.startDate.format('YYYY-MM-DD'));
  });
  $('.allow-empty-single-datepicker').on('cancel.daterangepicker', function(ev, picker) {
      $(this).val('');
  });

  $('.allow-empty-date-range').daterangepicker({
    locale: {
      format: 'YYYY-MM-DD',
      cancelLabel: 'Clear'
    },
    ranges: {
      'Today': [moment(), moment()],
      'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
      'Last 7 Days': [moment().subtract(7, 'days'), moment().subtract(1, 'days')],
      'Last 30 Days': [moment().subtract(30, 'days'), moment().subtract(1, 'days')],
      'This Month': [moment().startOf('month'), moment()],
      'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
    },
    alwaysShowCalendars: true,
    autoUpdateInput: false,
    buttonClasses: ['btn', 'btn-sm'],
    applyClass: 'btn-primary',
    cancelClass: 'btn-default',
    minDate: moment.utc().subtract(90, 'days').startOf('month'),
    maxDate: moment.utc()
  });
  $('.allow-empty-date-range').on('apply.daterangepicker', function(ev, picker) {
      $(this).val(picker.startDate.format('YYYY-MM-DD') + ' - ' + picker.endDate.format('YYYY-MM-DD'));
  });
  $('.allow-empty-date-range').on('cancel.daterangepicker', function(ev, picker) {
      $(this).val('');
  });

  // 只能选周一
  // https://github.com/feed-mob/feedmob/issues/9663
  $('.monday_datepicker').daterangepicker({
    singleDatePicker: true,
    minDate: moment.utc().subtract(90, 'days').startOf('month'),
    maxDate: moment.utc(),
    isInvalidDate: function(date) {
      if (date.format('dddd') != 'Monday') {
          return true;
      }
    }
  }).attr('autocomplete','off');

  function getCookie(name){
    var arr = document.cookie.match(new RegExp("(^| )"+name+"=([^;]*)(;|$)"));
    if(arr != null) return unescape(arr[2]); return null;
  }

  // MetsiMenu
  $('#side-menu').metisMenu();

  var side_bar_status = 'open';

  if (getCookie('side_bar_status') === 'close') {
    side_bar_status = 'close'
  }

  if (side_bar_status == 'open') {
    $('body').removeClass('mini-navbar');
  } else  {
    $('body').addClass('mini-navbar');
  }
}

$(document).on('turbolinks:load', domReady);
$(document).on('turbolinks:before-cache', function() {
  $('.select2').each(function() {
    $(this).select2().select2('destroy');
  });
  $('.tag-select2').each(function() {
    $(this).select2().select2('destroy');
  });
});
