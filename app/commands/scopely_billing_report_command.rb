class ScopelyBillingReportCommand < ReportBaseCommand
    prepend SimpleCommand
    include ErbBinding

    CLIENT_NAME = 'Scopely Billing'.freeze
    MAIL_GROUP_NAME = 'scopely_billing_monthly_report'.freeze
    CC_MAIL_GROUP_NAME = 'scopely_billing_monthly_report_cc'.freeze
    APP_NAMES = ["MONOPOLY GO!", "New YAHTZEE® With Buddies","Garden Joy - Design Game","Garden Joy: Design Game","Scrabble® GO-Classic Word Game","Scrabble® GO - New Word Game"].freeze

    # Define app groups for merging similar app names
    APP_GROUPS = {
      'Monopoly GO' => ["MONOPOLY GO!"],
      'New Yahtzee' => ["New YAHTZEE® With Buddies"],
      'Garden Joy' => ["Garden Joy - Design Game", "Garden Joy: Design Game"],
      'Scrabble GO' => ["Scrabble® GO-Classic Word Game", "Scrabble® GO - New Word Game"]
    }.freeze

    RESULT_ATTRIBUTES = %i[
      campaign_name
      installs
      installs_after
      spend_after
      spend
      mismatch_country_install
      over_cap_install
      manual_stopped_install
      dismatch_device_install
      mtti_sub_threshold_install
      install_decay_threshold_install
      rejected_by_24metrics_install
      os_version_blocked_install
      total_fraud_install
      total_fraud_saving
      mg_installs
      mg_gross
      mg_net
      gross_rate_rejected_calc
      net_rate
    ].freeze

    Result = Struct.new(*RESULT_ATTRIBUTES)

    attr_reader :start_date, :end_date, :client_id, :filename

    def initialize(start_date:, end_date:, opts: {}, **_)
      @start_date = start_date.to_date
      @end_date = end_date.to_date
      @filename = "Scopely_Billing_Report_#{@start_date}_#{@end_date}.csv"
      @client_id  = Client.where(name: 'Scopely').pluck(:id).first
    end

    def call
      Zip::OutputStream::write_buffer do |zipfile|

          if has_data?
            csv_stream = report_csv_stream
            zipfile.put_next_entry filename
            zipfile.print csv_stream
          else
            zipfile.put_next_entry "Scopely Billing Report - No Data #{start_date}_#{end_date}.csv"
            zipfile.print ''
          end

      end.string
    end

    def has_data?
      data.present?
    end

    def report_csv_stream
      @report_csv_stream ||= generate_csv_stream
    end

    def client_name
      CLIENT_NAME
    end

    def mail_group_name
      MAIL_GROUP_NAME
    end

    def cc_mail_group_name
      CC_MAIL_GROUP_NAME
    end

    private

    def generate_csv_stream
      CSV.generate(headers: true) do |csv|
        csv << header
        data.each do |row|
          csv_row = []
          csv_row << row.campaign_name
          csv_row << row.installs
          csv_row << row.installs_after
          csv_row << row.spend_after
          csv_row << row.spend
          csv_row << row.mismatch_country_install
          csv_row << row.over_cap_install
          csv_row << row.manual_stopped_install
          csv_row << row.dismatch_device_install
          csv_row << row.mtti_sub_threshold_install
          csv_row << row.install_decay_threshold_install
          csv_row << row.rejected_by_24metrics_install
          csv_row << row.os_version_blocked_install
          csv_row << row.total_fraud_install
          csv_row << row.total_fraud_saving
          csv_row << row.mg_installs
          csv_row << row.mg_gross
          csv_row << row.mg_net
          csv_row << row.gross_rate_rejected_calc
          csv_row << row.net_rate
          csv << csv_row
        end
        csv
      end
    end

    def data
      @data ||= begin
        db_spends_records = fetch_db_spends_records
        campaign_installs = get_campaign_installs
        fraud_install_records = get_fraud_install_records
        grouped_records = db_spends_records.group_by { |row| row['name'] }
        results = []

        # Process grouped apps first
        APP_GROUPS.each do |group_name, app_names|
          group_results = []

          app_names.each do |app_name|
            app_records = grouped_records[app_name] || []

            app_results = app_records.map do |row|
              installs = campaign_installs[[row['campaign_id'], row['campaign_name']]] || { installs: 0 }
              fraud_data = fraud_install_records[row['campaign_id'].to_s] || {}

              next if fraud_data['total_fraud_install'].to_i == 0 && installs[:installs].to_i == 0 && row['gross_spend'].to_f == 0

              click_url_ids = row['click_url_ids'].to_s.gsub(/[{}]/, '').split(',').map(&:to_i)

              click_url = ClickUrl.find_by_id(click_url_ids.first)
              if click_url.present?
                gross_cpi = click_url.gross_cpi_by_date(end_date).to_f
                net_cpi = click_url.net_cpi_by_date(end_date).to_f
              else
                gross_cpi = 0
                net_cpi = 0
              end

              total_fraud_saving = fraud_data['total_fraud_install'].to_i * gross_cpi

              Result.new(
                row['campaign_name'],
                installs[:installs],
                nil,
                nil,
                number_to_currency(row['gross_spend']),
                fraud_data['dismatch_country_install'].to_i,
                fraud_data['over_cap_install'].to_i,
                fraud_data['manual_stopped_install'].to_i,
                fraud_data['dismatch_device_install'].to_i,
                fraud_data['mtti_sub_thres_install'].to_i,
                fraud_data['install_decay_thres_install'].to_i,
                fraud_data['rejected_by_24metrics_install'].to_i,
                fraud_data['os_version_blocked_install'].to_i,
                fraud_data['total_fraud_install'].to_i,
                number_to_currency(total_fraud_saving),
                "",
                "",
                "",
                number_to_currency(gross_cpi),
                number_to_currency(net_cpi)
              )
            end.compact

            group_results.concat(app_results)
          end

          if group_results.present?
            results.concat(group_results)
            results << calculate_subtotal(group_results, "#{group_name} Total")
            results << Result.new(nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil)
          end
        end

        # Process individual apps (not in groups)
        grouped_app_names = APP_GROUPS.values.flatten
        individual_apps = APP_NAMES - grouped_app_names

        individual_apps.each do |app_name|
          app_records = grouped_records[app_name] || []

          app_results = app_records.map do |row|
            installs = campaign_installs[[row['campaign_id'], row['campaign_name']]] || { installs: 0 }
            fraud_data = fraud_install_records[row['campaign_id'].to_s] || {}

            next if fraud_data['total_fraud_install'].to_i == 0 && installs[:installs].to_i == 0 && row['gross_spend'].to_f == 0

            click_url_ids = row['click_url_ids'].to_s.gsub(/[{}]/, '').split(',').map(&:to_i)

            click_url = ClickUrl.find_by_id(click_url_ids.first)
            if click_url.present?
              gross_cpi = click_url.gross_cpi_by_date(end_date).to_f
              net_cpi = click_url.net_cpi_by_date(end_date).to_f
            else
              gross_cpi = 0
              net_cpi = 0
            end

            total_fraud_saving = fraud_data['total_fraud_install'].to_i * gross_cpi

            Result.new(
              row['campaign_name'],
              installs[:installs],
              nil,
              nil,
              number_to_currency(row['gross_spend']),
              fraud_data['dismatch_country_install'].to_i,
              fraud_data['over_cap_install'].to_i,
              fraud_data['manual_stopped_install'].to_i,
              fraud_data['dismatch_device_install'].to_i,
              fraud_data['mtti_sub_thres_install'].to_i,
              fraud_data['install_decay_thres_install'].to_i,
              fraud_data['rejected_by_24metrics_install'].to_i,
              fraud_data['os_version_blocked_install'].to_i,
              fraud_data['total_fraud_install'].to_i,
              number_to_currency(total_fraud_saving),
              "",
              "",
              "",
              number_to_currency(gross_cpi),
              number_to_currency(net_cpi)
            )
          end.compact

          results.concat(app_results)

          if app_results.present?
            results << calculate_subtotal(app_results, "Total")
            results << Result.new(nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil)
          end
        end

        results
      end
    end

    def calculate_subtotal(records, label)
      return Result.new(label) if records.empty?

      Result.new(
        label,
        records.sum { |r| r.installs.to_i },
        nil,
        nil,
        number_to_currency(records.sum { |r| r.spend.to_s.gsub(/[^\d.]/, '').to_f }),
        records.sum { |r| r.mismatch_country_install.to_i },
        records.sum { |r| r.over_cap_install.to_i },
        records.sum { |r| r.manual_stopped_install.to_i },
        records.sum { |r| r.dismatch_device_install.to_i },
        records.sum { |r| r.mtti_sub_threshold_install.to_i },
        records.sum { |r| r.install_decay_threshold_install.to_i },
        records.sum { |r| r.rejected_by_24metrics_install.to_i },
        records.sum { |r| r.os_version_blocked_install.to_i },
        records.sum { |r| r.total_fraud_install.to_i },
        number_to_currency(records.sum { |r| r.total_fraud_saving.to_s.gsub(/[^\d.]/, '').to_f }),
        nil,
        nil,
        nil,
        nil,
        nil
      )
    end

    def fetch_db_spends_records
      sql = <<-SQL
        SELECT
          ai.name,
          ds.campaign_id,
          c.name AS campaign_name,
          array_agg(DISTINCT ds.click_url_id) AS click_url_ids,
          sum(ds.net_spend_cents)/ 100.0  AS net_spend,
          sum(ds.gross_spend_cents)/ 100.0 AS gross_spend
        FROM
          direct_spends ds ,
          campaigns c ,
          app_infos ai
        WHERE
          ds.campaign_id = c.id
          AND c.app_info_id = ai.id
          AND ai.name in (#{APP_NAMES.map { |name| "'#{name}'" }.join(', ')})
          AND ds.spend_date BETWEEN '#{start_date}' AND '#{end_date}'
          AND ds.client_id = #{client_id}
        GROUP BY 1, 2, 3
        order by 1 ASC, 6 DESC
      SQL

      @db_spends_records ||= Campaign.connection.execute(sql,
        start_date: start_date,
        end_date: end_date,
        client_id: client_id
      )
    end

    def campaign_ids
      @campaign_ids ||= fetch_db_spends_records.map { |record| record['campaign_id'] }
    end

    def fetch_report_from_adjust_api
      command = Scopely::FetchReportFromAdjustApiCommand.call(start_date: start_date, end_date: end_date)
      if command.failure?
        errors.add(:create_scorecard_report, command.errors[:fetch_report_from_adjust_api].join(', '))
        return
      end

      @report_from_adjust_dash = command.result.group_by { |r| r.campaign_network }
    end

    def mapping_data
      @mapping_data ||= begin
        command = ScopelyCampaignMappingReportCommand.new(start_date: start_date, end_date: end_date)
        command.get_data
      end
    end

    def get_fraud_install_records
      return {} if campaign_ids.blank?
      sql = <<-SQL
        SELECT
          campaign_id AS campaign_id,
          campaign_name AS campaign_name,
          SUM(CASE WHEN status= 'dismatch_country'THEN install_count ELSE 0 END) AS dismatch_country_install,
          SUM(CASE WHEN status= 'over_cap'THEN install_count ELSE 0 END) AS over_cap_install,
          SUM(CASE WHEN status= 'manual_stopped'THEN install_count ELSE 0 END) AS manual_stopped_install,
          SUM(CASE WHEN status= 'dismatch_device'THEN install_count ELSE 0 END) AS dismatch_device_install,
          SUM(CASE WHEN status= 'mtti_sub_thres'THEN install_count ELSE 0 END) AS mtti_sub_thres_install,
          SUM(CASE WHEN status= 'install_decay_thres'THEN install_count ELSE 0 END) AS install_decay_thres_install,
          SUM(CASE WHEN status= 'rejected_by_24metrics'THEN install_count ELSE 0 END) AS rejected_by_24metrics_install,
          SUM(CASE WHEN status= 'os_version_blocked'THEN install_count ELSE 0 END) AS os_version_blocked_install ,
          sum(case when status in ('dismatch_country',  'over_cap', 'manual_stopped',
            'dismatch_device', 'mtti_sub_thres',
            'rejected_by_24metrics',
            'install_decay_thres',
            'os_version_blocked') then install_count else 0 end) total_fraud_install
        FROM v4_campaigns_view
        WHERE calculate_date BETWEEN '#{start_date}' AND '#{end_date}'
        AND campaign_id IN (#{campaign_ids.join(',')})
        AND status IN (
            'dismatch_country',  'over_cap', 'manual_stopped',
            'dismatch_device', 'mtti_sub_thres',
            'rejected_by_24metrics',
            'install_decay_thres',
            'os_version_blocked'
           )
        GROUP BY 1, 2
        ORDER BY 1, 2
      SQL

      ConversionRecordRedshift.connection.execute(sql,
        start_date: start_date,
        end_date: end_date,
        campaign_ids: campaign_ids
       ).to_a.index_by do |record|
        record['campaign_id'].to_s
      end
    end

    def get_installs_with_mapping(adjust_data)
      campaign_installs = {}

      mapping_data.each do |adjust_name, feedmob_name, campaign_id, vendor|
        next unless adjust_data[adjust_name]

        campaign_key = [campaign_id, feedmob_name]
        campaign_installs[campaign_key] ||= {
          installs: 0,
          vendor: vendor,
          adjust_campaigns: []
        }

        adjust_metrics = adjust_data[adjust_name].first
        campaign_installs[campaign_key][:installs] += adjust_metrics.installs.to_i
        campaign_installs[campaign_key][:adjust_campaigns] << adjust_name
      end
      campaign_installs
    end

    def get_campaign_installs
      fetch_report_from_adjust_api
      return if @report_from_adjust_dash.blank?

      get_installs_with_mapping(@report_from_adjust_dash)
    end

    def header
      [
        "Campaign",
        "Installs",
        "Installs After",
        "Spend After",
        "Spend",
        "Mismatch Country Install",
        "Over Cap Install",
        "Manual Stopped Install",
        "Dismatch Device Install",
        "MTTI Sub Threshold Install",
        "Install Decay Threshold Install",
        "Rejected by 24metrics Install",
        "OS Version Blocked Install",
        "Total Fraud Install",
        "Total Fraud Savings",
        "MG Installs",
        "MG Gross",
        "MG Net",
        "Gross Rate(rejected calc)",
        "Net Rate"
      ]
    end
end